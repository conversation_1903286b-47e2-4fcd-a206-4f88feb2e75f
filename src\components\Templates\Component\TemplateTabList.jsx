import { Card, Radio } from "antd";
import { Braces, LayoutPanelTop, List } from "lucide-react";
import React, { useEffect, useState } from "react";
import DetailTab from "./DetailTab";
import StructureTab from "./StructureTab";

const TemplateTabList = ({ pages, formData, setFormData, template }) => {
  const [placeholderInput, setPlaceholderInput] = useState("");
  const [previewMode, setPreviewMode] = useState("template_details");
  const [editingComponent, setEditingComponent] = useState(null);

  const tabContents = {
    template_details: {
      key: "template_details",
      label: "Template Details",
      icon: <List className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <DetailTab
          formData={formData}
          setFormData={setFormData}
          template={template}
        />
      ),
    },
    structure: {
      key: "structure",
      label: "Structure",
      icon: <LayoutPanelTop className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <StructureTab
          pages={pages}
          formData={formData}
          setFormData={setFormData}
          template={template}
        />
      ),
    },
    content: {
      key: "content",
      label: "Content",
      icon: <Braces className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: <></>,
    },
  };

  const autoDetectPlaceholders = () => {
    const content = formData?.html_content;
    // if (!content) return;
    const regex = /\$\{([^}]+)\}/g;
    const matches = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
      if (!matches.includes(match[1])) {
        matches.push(match[1]);
      }
    }
    // ...formData.placeholders,
    setFormData({
      ...formData,
      placeholders: [...new Set([...matches])],
    });
  };
  useEffect(() => {
    const timeout = setTimeout(() => {
      // if (formData?.html_content) {
      // console.log("isCallSignatureDeclaration....");
      autoDetectPlaceholders();
      // }
    }, 1000);

    return () => clearTimeout(timeout);
  }, [formData?.html_content]);
  return (
    <div className="tw-space-y-4">
      {/* Preview/Code Toggle */}
      <div className="tw-p-6  tw-rounded-xl  tw-p-4">
        <Radio.Group
          value={previewMode}
          onChange={(e) => setPreviewMode(e.target.value)}
          buttonStyle="solid"
          size="large"
          className="component-tab-list tw-w-full tw-p-[2px] tw-border tw-border-[#2563EB] tw-rounded-[10px]"
        >
          {Object.values(tabContents)?.map((tab) => (
            <Radio.Button
              key={tab.key}
              value={tab.key}
              className="tw-flex-1 tw-text-center !tw-rounded-[10px] before:!tw-w-0 tw-border-0 border-b-"
              style={{ width: "33.33%" }}
            >
              <div className="tw-flex tw-items-center tw-justify-center">
                {tab?.icon}
                {tab?.label}
              </div>
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>
      <div className="tw-space-y-4">
        {tabContents[previewMode]?.content ? (
          tabContents[previewMode]?.content
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default TemplateTabList;
