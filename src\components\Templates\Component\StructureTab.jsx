import React, { useState, useEffect, useRef } from "react";
import { Input, Select, Switch, Tooltip, Button, message } from "antd";
import {
  ChevronLeft,
  GripVertical,
  Search,
  Loader2,
  Plus,
  Trash2,
  Eye,
  FileText,
} from "lucide-react";
import { useDrag, useDrop, DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
// import useHttp from "../../../hooks/use-http"; // Commented for future API use
import useStorage from "../../../hooks/use-storage"; // Using JSON storage

import { generateGlobalPreviewHTML } from "../../Components/content";
import TemplateLibrary from "./TemplateLibrary";
import TemplatePreview from "./TemplatePreview";
import TemplateStructure from "./TemplateStructure";

// DND Types
const DND_TYPES = {
  PAGE_ITEM: "PAGE_ITEM",
};

// Device sizes for responsive preview
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const StructureTab = ({ pages, formData, setFormData, template }) => {
  // const api = useHttp(); // Commented for future API use
  const api = useStorage(); // Using JSON storage
  const [pagesList, setPagesList] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLibraryOpen, setIsPageLibraryOpen] = useState(true);
  const [isTemplateStructureOpen, setIsTemplateStructureOpen] = useState(true);
  const [selectedPageId, setSelectedPageId] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [scale, setScale] = useState(1);
  const containerRef = useRef(null);

  // Get current device dimensions (default to laptop for template preview)
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES.laptop;

  // Responsive detection
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Fetch pages on component mount
  useEffect(() => {
    // setIsLoading(true);
    setPagesList(pages || []);
    // api.sendRequest(
    //   CONSTANTS.API.pages.get,
    //   (res) => {
    //     console.log("Pages fetched:", res);
    //     setPages(res);
    //     setIsLoading(false);
    //   },
    //   null,
    //   null,
    //   (error) => {
    //     console.error("Error fetching pages:", error);
    //     setIsLoading(false);
    //   }
    // );
  }, [pages]);

  // Initialize template pages if not exists
  useEffect(() => {
    if (!formData.pages) {
      setFormData({ ...formData, pages: [] });
    }
  }, [formData, setFormData]);

  // Scale calculation function for preview
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    const availableWidth = bounds.width - 30;
    const availableHeight = bounds.height - 30;
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    setScale(Math.min(widthScale, heightScale, 1));
  };

  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [deviceWidth, deviceHeight]);

  // Global onChange handler for all component fields
  const handlePagesFieldChange = (index, field, value) => {
    const updated = [...formData.pages];
    updated[index] = { ...updated[index], [field]: value };
    console.log("Updated pages:", updated);
    setFormData((prevData) => ({
      ...prevData,
      pages: updated,
      // pageComponentList: updated,
    }));
  };
  console.log(formData, "formData");
  // Remove page from template
  const removePageFromTemplate = (pageId) => {
    const updatedPages = formData.pages?.filter((p) => p.order != pageId) || [];
    setFormData({ ...formData, pages: updatedPages });
    if (selectedPageId === pageId) {
      setSelectedPageId(null);
    }
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...formData?.pages];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setFormData((prevData) => ({
      ...prevData,
      pages: updatedComponents,
      // pages: updatedComponents,
    }));
  };

  // Update page in template
  const updatePageInTemplate = (pageId, updates) => {
    const updatedPages =
      formData.pages?.map((p) =>
        p.id === pageId ? { ...p, ...updates } : p
      ) || [];
    setFormData({ ...formData, pages: updatedPages });
  };

  // Save template structure
  const handleSaveStructure = async () => {
    try {
      // Generate full template content from current structure
      const fullTemplateContent = generateTemplateHTML();

      // Create templateComponentList from current pages
      const templateComponentList =
        formData.pages?.map((page, index) => ({
          version: "v1",
          url: page.slug || `/${page.name.toLowerCase().replace(/\s+/g, "-")}`,
          repeator: "single",
          position: index,
          navbar: page.navbar || false,
          navbar_position_index: page.navbar_position_index || index,
        })) || [];

      // Update formData with generated content
      const updatedFormData = {
        ...formData,
        full_template_content: fullTemplateContent,
        templateComponentList: templateComponentList,
        content: formData.content || [],
      };

      setFormData(updatedFormData);

      // If template exists, save it
      if (template && template.id) {
        api
          .directOperation("templates", "update", template.id, updatedFormData)
          .then((res) => {
            console.log("Template structure saved successfully:", res);
            message.success("Template structure saved successfully!");
          })
          .catch((error) => {
            console.error("Error saving template structure:", error);
            message.error(
              "Failed to save template structure. Please try again."
            );
          });
      } else {
        message.info(
          "Please save the template details first before saving structure."
        );
      }
    } catch (error) {
      console.error("Error generating template structure:", error);
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  // Generate template HTML from current structure
  const generateTemplateHTML = () => {
    if (!formData.pages || formData.pages.length === 0) {
      return '<div class="template-placeholder">No pages added to template</div>';
    }

    const pagesHTML = formData.pages
      .map((page, index) => {
        return `
        <div class="template-page" data-page-id="${
          page.id
        }" data-position="${index}">
          <div class="page-content">
            ${
              page.full_page_content ||
              `<div class="page-placeholder">Page: ${page.name}</div>`
            }
          </div>
        </div>
      `;
      })
      .join("\n");

    return `
      <div class="template-container">
        <div class="template-pages">
          ${pagesHTML}
        </div>
      </div>
    `;
  };

  // Get filtered pages (exclude already added pages)
  const availablePages = pagesList.filter(
    (page) => !formData.pages?.some((tp) => tp.id === page.id)
  );

  const filteredPages = availablePages.filter((page) =>
    page.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden tw-relative">
        {/* Mobile Backdrop */}
        {isMobile && (isPageLibraryOpen || isTemplateStructureOpen) && (
          <div
            className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-40"
            onClick={() => {
              setIsPageLibraryOpen(false);
              setIsTemplateStructureOpen(false);
            }}
          />
        )}

        <TemplateLibrary
          isPageLibraryOpen={isPageLibraryOpen}
          setIsPageLibraryOpen={setIsPageLibraryOpen}
          isMobile={isMobile}
          isTablet={isTablet}
          isLoading={isLoading}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filteredPages={filteredPages}
        />

        {/* Center Preview Area */}
        <TemplatePreview
          isPageLibraryOpen={isPageLibraryOpen}
          setIsPageLibraryOpen={setIsPageLibraryOpen}
          isTemplateStructureOpen={isTemplateStructureOpen}
          setIsTemplateStructureOpen={setIsTemplateStructureOpen}
          formData={formData}
          setFormData={setFormData}
          pages={pages}
        />

        {/* Right Sidebar - Template Structure */}

        <TemplateStructure
          isTemplateStructureOpen={isTemplateStructureOpen}
          setIsTemplateStructureOpen={setIsTemplateStructureOpen}
          pageData={formData}
          formData={formData}
          setFormData={setFormData}
          removePageFromTemplate={removePageFromTemplate}
          onComponentFieldChange={handlePagesFieldChange}
          moveComponent={moveComponent}
        />

        {/* Save Button */}
        <div className="tw-fixed tw-bottom-6 tw-right-6 tw-z-50">
          <Button
            type="primary"
            size="large"
            onClick={handleSaveStructure}
            className="tw-px-8 tw-py-3 tw-h-auto tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0 tw-shadow-lg"
          >
            Save Template Structure
          </Button>
        </div>
      </div>
    </DndProvider>
  );
};

export default StructureTab;
